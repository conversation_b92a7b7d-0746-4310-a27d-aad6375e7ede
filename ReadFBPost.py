import requests
import os
import sys
import json
from dotenv import load_dotenv

load_dotenv() # Load environment variables from .env file

def get_recent_facebook_posts(group_id, access_token, post_limit=2):
    """
    Fetch recent posts from a specific Facebook group using the Graph API.

    :param group_id: The ID of the Facebook group.
    :param access_token: Your Facebook Graph API access token.
    :param post_limit: Number of recent posts to fetch (default is 2).
    :return: List of recent posts.
    """
    # It's good practice to use the latest stable API version.
    # Please check Facebook's developer documentation for the current latest version.
    url = f"https://graph.facebook.com/v22.0/{group_id}/feed"
    params = {
        'access_token': access_token,
        'limit': post_limit,
        'fields': 'id,message,created_time,full_picture,attachments{type,media,target,url,subattachments}'
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()

        if 'data' in data:
            return data['data']
        else:
            print("No posts found or insufficient permissions.")
            return []

    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
        return []

def load_published_posts(filename="published_posts.txt"):
    """
    Load the list of already published post IDs from a local file.

    :param filename: Name of the file to store published post IDs.
    :return: Set of published post IDs.
    """
    try:
        if os.path.exists(filename):
            with open(filename, 'r') as f:
                return set(line.strip() for line in f if line.strip())
        return set()
    except Exception as e:
        print(f"Error loading published posts: {e}")
        return set()

def save_published_posts(published_posts, filename="published_posts.txt"):
    """
    Save the list of published post IDs to a local file.

    :param published_posts: Set of published post IDs.
    :param filename: Name of the file to store published post IDs.
    """
    try:
        with open(filename, 'w') as f:
            for post_id in published_posts:
                f.write(f"{post_id}\n")
    except Exception as e:
        print(f"Error saving published posts: {e}")

def extract_media_from_post(post):
    """
    Extract media URLs from a Facebook post.

    :param post: Facebook post data.
    :return: List of dictionaries with media URLs and types.
    """
    media_items = []

    # Check for attachments first (more reliable for videos)
    attachments = post.get('attachments', {})
    if isinstance(attachments, dict) and 'data' in attachments:
        for attachment in attachments['data']:
            # Handle video attachments
            if attachment.get('type') in ['video_inline', 'video_autoplay', 'video', 'video_direct_response']:
                # Try different video URL sources
                video_url = None

                # Method 1: Check media.source
                if attachment.get('media') and 'source' in attachment['media']:
                    video_url = attachment['media']['source']

                # Method 2: Check target.url for video links
                elif attachment.get('target') and attachment['target'].get('url'):
                    target_url = attachment['target']['url']
                    # Facebook video URLs often redirect, but we can try
                    if 'facebook.com' in target_url and 'videos' in target_url:
                        video_url = target_url

                # Method 3: Check url field directly
                elif attachment.get('url'):
                    video_url = attachment['url']

                if video_url and not any(item['url'] == video_url for item in media_items):
                    media_items.append({'url': video_url, 'type': 'video'})

            # Handle photo attachments
            elif attachment.get('type') == 'photo' and attachment.get('media'):
                if 'image' in attachment['media'] and 'src' in attachment['media']['image']:
                    media_url = attachment['media']['image']['src']
                    if media_url and not any(item['url'] == media_url for item in media_items):
                        media_items.append({'url': media_url, 'type': 'photo'})

    # Check for full_picture as fallback (often just thumbnail for videos)
    if post.get('full_picture') and not media_items:
        full_picture = post['full_picture']
        if 'safe_image' not in full_picture and len(full_picture) > 50:
            # This is likely just a thumbnail, but better than nothing
            media_type = 'photo'  # Default to photo since we can't get actual video
            media_items.append({'url': full_picture, 'type': media_type})

    return media_items

def send_photo_to_telegram(bot_token, channel_id, photo_url, caption=None):
    """
    Send a photo to a Telegram channel using HTTP API.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param photo_url: URL of the photo to send.
    :param caption: Optional caption for the photo.
    :return: True if successful, False otherwise.
    """
    try:
        # Telegram Bot API URL for sending photos
        url = f"https://api.telegram.org/bot{bot_token}/sendPhoto"

        # Prepare the payload
        payload = {
            'chat_id': channel_id,
            'photo': photo_url,
            'parse_mode': 'HTML'
        }

        if caption:
            payload['caption'] = caption

        # Send the request
        response = requests.post(url, data=payload)
        response.raise_for_status()

        # Parse the response
        result = response.json()

        if result.get('ok'):
            return True
        else:
            error_description = result.get('description', 'Unknown error')
            print(f"❌ Telegram API error sending photo: {error_description}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error sending photo to Telegram: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error sending photo to Telegram: {e}")
        return False

def send_video_to_telegram(bot_token, channel_id, video_url, caption=None):
    """
    Send a video to a Telegram channel using HTTP API.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param video_url: URL of the video to send.
    :param caption: Optional caption for the video.
    :return: True if successful, False otherwise.
    """
    try:
        # Telegram Bot API URL for sending videos
        url = f"https://api.telegram.org/bot{bot_token}/sendVideo"

        # Prepare the payload
        payload = {
            'chat_id': channel_id,
            'video': video_url,
            'parse_mode': 'HTML'
        }

        if caption:
            payload['caption'] = caption

        # Send the request
        response = requests.post(url, data=payload)
        response.raise_for_status()

        # Parse the response
        result = response.json()

        if result.get('ok'):
            return True
        else:
            error_description = result.get('description', 'Unknown error')
            print(f"❌ Telegram API error sending video: {error_description}")

            # Log additional details for debugging
            if 'bad request' in error_description.lower():
                print(f"   Video URL: {video_url[:100]}...")
                if 'file size' in error_description.lower():
                    print("   💡 Tip: Video file may be too large for Telegram")
                elif 'format' in error_description.lower():
                    print("   💡 Tip: Video format may not be supported by Telegram")
                else:
                    print("   💡 Tip: Video URL may not be directly accessible by Telegram")

            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error sending video to Telegram: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error sending video to Telegram: {e}")
        return False

def send_video_as_document(bot_token, channel_id, video_url, caption=None):
    """
    Send a video as a document to Telegram (fallback method).

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param video_url: URL of the video to send as document.
    :param caption: Optional caption for the document.
    :return: True if successful, False otherwise.
    """
    try:
        # Telegram Bot API URL for sending documents
        url = f"https://api.telegram.org/bot{bot_token}/sendDocument"

        # Prepare the payload
        payload = {
            'chat_id': channel_id,
            'document': video_url,
            'parse_mode': 'HTML'
        }

        if caption:
            payload['caption'] = caption

        # Send the request
        response = requests.post(url, data=payload)
        response.raise_for_status()

        # Parse the response
        result = response.json()

        if result.get('ok'):
            return True
        else:
            error_description = result.get('description', 'Unknown error')
            print(f"❌ Telegram API error sending video as document: {error_description}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error sending video as document to Telegram: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error sending video as document to Telegram: {e}")
        return False

def send_link_to_telegram(bot_token, channel_id, message_with_link):
    """
    Send a message with a link to Telegram (fallback for videos that can't be directly sent).

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param message_with_link: Message containing the link.
    :return: True if successful, False otherwise.
    """
    try:
        # Telegram Bot API URL
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"

        # Prepare the payload
        payload = {
            'chat_id': channel_id,
            'text': message_with_link,
            'parse_mode': 'HTML',
            'disable_web_page_preview': False  # Allow preview for video links
        }

        # Send the request
        response = requests.post(url, data=payload)
        response.raise_for_status()

        # Parse the response
        result = response.json()

        if result.get('ok'):
            return True
        else:
            error_description = result.get('description', 'Unknown error')
            print(f"❌ Telegram API error sending link: {error_description}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error sending link to Telegram: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error sending link to Telegram: {e}")
        return False

def send_post_to_telegram(bot_token, channel_id, post_message, media_items):
    """
    Send a Facebook post to Telegram, handling both text and media.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param post_message: Text content of the post.
    :param media_items: List of dictionaries with media URLs and types.
    :return: True if successful, False otherwise.
    """
    success_count = 0

    # Format the text message
    telegram_message = post_message

    if not media_items:
        # Text-only post
        return send_to_telegram(bot_token, channel_id, telegram_message)

    else:
        # Post with media
        # Send the first media item with the caption (text)
        first_media = media_items[0]
        if first_media['type'] == 'video':
            success = send_video_to_telegram(bot_token, channel_id, first_media['url'], telegram_message)

            # If video sending fails, try multiple fallback methods
            if not success:
                print("🔄 Video sending failed, trying as document...")
                success = send_video_as_document(bot_token, channel_id, first_media['url'], telegram_message)

                # If document also fails, send as link with Facebook URL
                if not success:
                    print("🔄 Document sending also failed, sending as link...")
                    if 'facebook.com' in first_media['url']:
                        link_message = f"{telegram_message}\n\n🎥 <a href=\"{first_media['url']}\">Click To Watch the Video</a>"
                    else:
                        link_message = f"{telegram_message}\n\n🎥 <a href=\"{first_media['url']}\">Click To Watch the Video</a>"
                    success = send_link_to_telegram(bot_token, channel_id, link_message)
        else:
            success = send_photo_to_telegram(bot_token, channel_id, first_media['url'], telegram_message)

        if success:
            success_count += 1

        # Send additional media without captions
        for media_item in media_items[1:]:
            if media_item['type'] == 'video':
                success = send_video_to_telegram(bot_token, channel_id, media_item['url'])

                # If video sending fails, try sending as document, then as link
                if not success:
                    success = send_video_as_document(bot_token, channel_id, media_item['url'])

                    if not success:
                        link_message = f"🎥 <a href=\"{media_item['url']}\">Click To Watch the Video</a>"
                        success = send_link_to_telegram(bot_token, channel_id, link_message)
            else:
                success = send_photo_to_telegram(bot_token, channel_id, media_item['url'])

            if success:
                success_count += 1

        # Consider it successful if at least the main content was sent
        return success_count > 0

def send_to_telegram(bot_token, channel_id, message):
    """
    Send a message to a Telegram channel using HTTP API.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param message: Message to send.
    :return: True if successful, False otherwise.
    """
    try:
        # Telegram Bot API URL
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"

        # Prepare the payload
        payload = {
            'chat_id': channel_id,
            'text': message,
            'parse_mode': 'HTML'
        }

        # Send the request
        response = requests.post(url, data=payload)
        response.raise_for_status()

        # Parse the response
        result = response.json()

        if result.get('ok'):
            return True
        else:
            error_description = result.get('description', 'Unknown error')
            print(f"❌ Telegram API error: {error_description}")

            # Provide helpful tips based on common errors
            if "chat not found" in error_description.lower():
                print("   💡 Tip: Make sure the channel ID is correct and the bot is added to the channel")
            elif "forbidden" in error_description.lower():
                print("   💡 Tip: Make sure the bot has permission to send messages in the channel")
            elif "bot was blocked" in error_description.lower():
                print("   💡 Tip: The bot was blocked. Make sure it's added as an admin to the channel")

            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error sending to Telegram: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error sending to Telegram: {e}")
        return False

def test_telegram_connection(bot_token, channel_id):
    """
    Test the Telegram bot connection and permissions using HTTP API.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :return: True if connection is successful, False otherwise.
    """
    try:
        # Test bot info first
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response = requests.get(url)
        response.raise_for_status()

        result = response.json()
        if result.get('ok'):
            bot_info = result.get('result', {})
            username = bot_info.get('username', 'Unknown')
            first_name = bot_info.get('first_name', 'Unknown')
            print(f"✅ Bot connected: @{username} ({first_name})")
        else:
            print(f"❌ Failed to get bot info: {result.get('description', 'Unknown error')}")
            return False

        # Test sending a simple message
        test_message = "🧪 <b>Test Message</b>\n\nThis is a test message from your Facebook Posts bot."
        if send_to_telegram(bot_token, channel_id, test_message):
            print(f"✅ Test message sent successfully!")
            return True
        else:
            print(f"❌ Failed to send test message")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error during connection test: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during connection test: {e}")
        return False

if __name__ == "__main__":
    # Check for test mode
    test_mode = len(sys.argv) > 1 and sys.argv[1] == "--test"

    # Load credentials from environment variables
    GROUP_ID = os.getenv("GROUP_ID")
    ACCESS_TOKEN = os.getenv("ACCESS_TOKEN")
    TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
    TELEGRAM_CHANNEL_ID = os.getenv("TELEGRAM_CHANNEL_ID")

    # Check required Facebook credentials
    if not GROUP_ID or not ACCESS_TOKEN:
        print("Error: GROUP_ID and ACCESS_TOKEN must be set in the .env file.")
        sys.exit(1)

    # Check Telegram credentials
    if not TELEGRAM_BOT_TOKEN or not TELEGRAM_CHANNEL_ID:
        print("Warning: TELEGRAM_BOT_TOKEN and TELEGRAM_CHANNEL_ID not set. Posts will not be published to Telegram.")
        telegram_enabled = False
    else:
        telegram_enabled = True

        # Test Telegram connection if in test mode
        if test_mode:
            if test_telegram_connection(TELEGRAM_BOT_TOKEN, TELEGRAM_CHANNEL_ID):
                print("✅ Telegram test completed successfully!")
            else:
                print("❌ Telegram test failed!")
            sys.exit(0)

    # Load previously published posts
    published_posts = load_published_posts()

    # Fetch the 2 most recent posts
    recent_posts = get_recent_facebook_posts(GROUP_ID, ACCESS_TOKEN)

    # Reverse the order of posts so the most recent Facebook post
    # appears as the latest message in Telegram
    recent_posts_reversed = list(reversed(recent_posts))

    # Process each post (now in reverse chronological order for Telegram)
    new_posts_published = False
    for i, post in enumerate(recent_posts_reversed, start=1):
        post_id = post.get('id', '')
        post_message = post.get('message', 'No message content')

        # Extract media from the post
        media_items = extract_media_from_post(post)

        # Check if this post has already been published to Telegram
        if post_id and post_id not in published_posts:
            if telegram_enabled:
                # Send to Telegram with media support - only mark as published if successful
                if send_post_to_telegram(TELEGRAM_BOT_TOKEN, TELEGRAM_CHANNEL_ID, post_message, media_items):
                    published_posts.add(post_id)
                    new_posts_published = True

    # Save updated published posts list
    if new_posts_published:
        save_published_posts(published_posts)