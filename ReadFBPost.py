import requests
import os
import sys
import json
from dotenv import load_dotenv

load_dotenv() # Load environment variables from .env file

def get_recent_facebook_posts(group_id, access_token, post_limit=4):
    """
    Fetch recent posts from a specific Facebook group using the Graph API.

    :param group_id: The ID of the Facebook group.
    :param access_token: Your Facebook Graph API access token.
    :param post_limit: Number of recent posts to fetch (default is 2).
    :return: List of recent posts.
    """
    # It's good practice to use the latest stable API version.
    # Please check Facebook's developer documentation for the current latest version.
    url = f"https://graph.facebook.com/v22.0/{group_id}/feed"
    params = {
        'access_token': access_token,
        'limit': post_limit,
        'fields': 'id,message,created_time,full_picture,attachments'
    }

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()

        if 'data' in data:
            # Debug: Print the structure of the first post to understand what we're getting
            if data['data'] and len(data['data']) > 0:
                print("🔍 Debug: First post structure:")
                first_post = data['data'][0]
                print(f"   Keys available: {list(first_post.keys())}")
                if 'attachments' in first_post:
                    print(f"   Attachments structure: {first_post['attachments']}")
                print("-" * 30)

            return data['data']
        else:
            print("No posts found or insufficient permissions.")
            return []

    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
        return []

def load_published_posts(filename="published_posts.txt"):
    """
    Load the list of already published post IDs from a local file.

    :param filename: Name of the file to store published post IDs.
    :return: Set of published post IDs.
    """
    try:
        if os.path.exists(filename):
            with open(filename, 'r') as f:
                return set(line.strip() for line in f if line.strip())
        return set()
    except Exception as e:
        print(f"Error loading published posts: {e}")
        return set()

def save_published_posts(published_posts, filename="published_posts.txt"):
    """
    Save the list of published post IDs to a local file.

    :param published_posts: Set of published post IDs.
    :param filename: Name of the file to store published post IDs.
    """
    try:
        with open(filename, 'w') as f:
            for post_id in published_posts:
                f.write(f"{post_id}\n")
    except Exception as e:
        print(f"Error saving published posts: {e}")

def extract_media_from_post(post):
    """
    Extract media URLs from a Facebook post.

    :param post: Facebook post data.
    :return: List of media URLs.
    """
    media_urls = []

    # Check for full_picture (main image)
    if post.get('full_picture'):
        # Only add if it's not a default Facebook image
        full_picture = post['full_picture']
        if 'safe_image' not in full_picture and len(full_picture) > 50:  # Basic filter for actual images
            media_urls.append(full_picture)

    # Check for attachments (simplified approach)
    attachments = post.get('attachments', {})
    if isinstance(attachments, dict) and 'data' in attachments:
        for attachment in attachments['data']:
            # Look for different types of media URLs in attachments
            if attachment.get('type') == 'photo' and attachment.get('media'):
                if 'image' in attachment['media'] and 'src' in attachment['media']['image']:
                    media_url = attachment['media']['image']['src']
                    if media_url and media_url not in media_urls:
                        media_urls.append(media_url)

            # Check for target URL that might contain images
            elif attachment.get('target') and attachment['target'].get('url'):
                target_url = attachment['target']['url']
                # This might be a link to an image or album - we'll try it
                if any(ext in target_url.lower() for ext in ['.jpg', '.jpeg', '.png', '.gif']):
                    if target_url not in media_urls:
                        media_urls.append(target_url)

    return media_urls

def send_photo_to_telegram(bot_token, channel_id, photo_url, caption=None):
    """
    Send a photo to a Telegram channel using HTTP API.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param photo_url: URL of the photo to send.
    :param caption: Optional caption for the photo.
    :return: True if successful, False otherwise.
    """
    try:
        print(f"🖼️ Attempting to send photo to Telegram channel: {channel_id}")

        # Telegram Bot API URL for sending photos
        url = f"https://api.telegram.org/bot{bot_token}/sendPhoto"

        # Prepare the payload
        payload = {
            'chat_id': channel_id,
            'photo': photo_url,
            'parse_mode': 'HTML'
        }

        if caption:
            payload['caption'] = caption

        # Send the request
        response = requests.post(url, data=payload)
        response.raise_for_status()

        # Parse the response
        result = response.json()

        if result.get('ok'):
            message_id = result.get('result', {}).get('message_id')
            print(f"✅ Photo sent successfully! Message ID: {message_id}")
            return True
        else:
            error_description = result.get('description', 'Unknown error')
            print(f"❌ Telegram API error sending photo: {error_description}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error sending photo to Telegram: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error sending photo to Telegram: {e}")
        return False

def send_post_to_telegram(bot_token, channel_id, post_message, media_urls):
    """
    Send a Facebook post to Telegram, handling both text and media.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param post_message: Text content of the post.
    :param media_urls: List of media URLs from the post.
    :return: True if successful, False otherwise.
    """
    success_count = 0

    # Format the text message
    telegram_message = f"<b>New Facebook Post</b>\n\n{post_message}"

    if not media_urls:
        # Text-only post
        print(f"📝 Sending text-only post...")
        return send_to_telegram(bot_token, channel_id, telegram_message)

    else:
        # Post with media
        print(f"📸 Sending post with {len(media_urls)} media item(s)...")

        # Send the first image with the caption (text)
        if send_photo_to_telegram(bot_token, channel_id, media_urls[0], telegram_message):
            success_count += 1
            print(f"✅ Sent main photo with caption")
        else:
            print(f"❌ Failed to send main photo with caption")

        # Send additional images without captions
        for i, media_url in enumerate(media_urls[1:], 2):
            if send_photo_to_telegram(bot_token, channel_id, media_url):
                success_count += 1
                print(f"✅ Sent additional photo {i}")
            else:
                print(f"❌ Failed to send additional photo {i}")

        # Consider it successful if at least the main content was sent
        return success_count > 0

def send_to_telegram(bot_token, channel_id, message):
    """
    Send a message to a Telegram channel using HTTP API.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :param message: Message to send.
    :return: True if successful, False otherwise.
    """
    try:
        print(f"🔄 Attempting to send message to Telegram channel: {channel_id}")

        # Telegram Bot API URL
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"

        # Prepare the payload
        payload = {
            'chat_id': channel_id,
            'text': message,
            'parse_mode': 'HTML'
        }

        # Send the request
        response = requests.post(url, data=payload)
        response.raise_for_status()

        # Parse the response
        result = response.json()

        if result.get('ok'):
            message_id = result.get('result', {}).get('message_id')
            print(f"✅ Message sent successfully! Message ID: {message_id}")
            return True
        else:
            error_description = result.get('description', 'Unknown error')
            print(f"❌ Telegram API error: {error_description}")

            # Provide helpful tips based on common errors
            if "chat not found" in error_description.lower():
                print("   💡 Tip: Make sure the channel ID is correct and the bot is added to the channel")
            elif "forbidden" in error_description.lower():
                print("   💡 Tip: Make sure the bot has permission to send messages in the channel")
            elif "bot was blocked" in error_description.lower():
                print("   💡 Tip: The bot was blocked. Make sure it's added as an admin to the channel")

            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error sending to Telegram: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error sending to Telegram: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False

def test_telegram_connection(bot_token, channel_id):
    """
    Test the Telegram bot connection and permissions using HTTP API.

    :param bot_token: Telegram bot token.
    :param channel_id: Telegram channel ID.
    :return: True if connection is successful, False otherwise.
    """
    try:
        print(f"🧪 Testing Telegram connection...")

        # Test bot info first
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response = requests.get(url)
        response.raise_for_status()

        result = response.json()
        if result.get('ok'):
            bot_info = result.get('result', {})
            username = bot_info.get('username', 'Unknown')
            first_name = bot_info.get('first_name', 'Unknown')
            print(f"✅ Bot connected: @{username} ({first_name})")
        else:
            print(f"❌ Failed to get bot info: {result.get('description', 'Unknown error')}")
            return False

        # Test sending a simple message
        test_message = "🧪 <b>Test Message</b>\n\nThis is a test message from your Facebook Posts bot."
        if send_to_telegram(bot_token, channel_id, test_message):
            print(f"✅ Test message sent successfully!")
            return True
        else:
            print(f"❌ Failed to send test message")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error during connection test: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during connection test: {e}")
        return False

if __name__ == "__main__":
    # Check for test mode
    test_mode = len(sys.argv) > 1 and sys.argv[1] == "--test"

    # Load credentials from environment variables
    GROUP_ID = os.getenv("GROUP_ID")
    ACCESS_TOKEN = os.getenv("ACCESS_TOKEN")
    TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
    TELEGRAM_CHANNEL_ID = os.getenv("TELEGRAM_CHANNEL_ID")

    # Check required Facebook credentials
    if not GROUP_ID or not ACCESS_TOKEN:
        print("Error: GROUP_ID and ACCESS_TOKEN must be set in the .env file.")
        sys.exit(1)

    # Check Telegram credentials
    if not TELEGRAM_BOT_TOKEN or not TELEGRAM_CHANNEL_ID:
        print("Warning: TELEGRAM_BOT_TOKEN and TELEGRAM_CHANNEL_ID not set. Posts will not be published to Telegram.")
        telegram_enabled = False
    else:
        telegram_enabled = True

        # Test Telegram connection if in test mode
        if test_mode:
            print("🧪 Running in test mode - testing Telegram connection...")
            if test_telegram_connection(TELEGRAM_BOT_TOKEN, TELEGRAM_CHANNEL_ID):
                print("✅ Telegram test completed successfully!")
            else:
                print("❌ Telegram test failed!")
            sys.exit(0)

    # Load previously published posts
    published_posts = load_published_posts()

    # Fetch the 2 most recent posts
    recent_posts = get_recent_facebook_posts(GROUP_ID, ACCESS_TOKEN)

    # Reverse the order of posts so the most recent Facebook post
    # appears as the latest message in Telegram
    recent_posts_reversed = list(reversed(recent_posts))

    # Process each post (now in reverse chronological order for Telegram)
    new_posts_published = False
    for i, post in enumerate(recent_posts_reversed, start=1):
        post_id = post.get('id', '')
        post_message = post.get('message', 'No message content')

        # Extract media from the post
        media_urls = extract_media_from_post(post)

        print(f"Post {i} (ID: {post_id}) - Processing in reverse chronological order:")
        print(post_message)
        if media_urls:
            print(f"📸 Found {len(media_urls)} media item(s)")
            for j, url in enumerate(media_urls, 1):
                print(f"   Media {j}: {url[:80]}...")
        else:
            print("📝 Text-only post")
        print("-" * 50)

        # Check if this post has already been published to Telegram
        if post_id and post_id not in published_posts:
            if telegram_enabled:
                # Send to Telegram with media support - only mark as published if successful
                print(f"📤 Attempting to send Post {i} to Telegram...")
                if send_post_to_telegram(TELEGRAM_BOT_TOKEN, TELEGRAM_CHANNEL_ID, post_message, media_urls):
                    print(f"✅ Post {i} successfully sent to Telegram")
                    published_posts.add(post_id)
                    new_posts_published = True
                else:
                    print(f"❌ Failed to send Post {i} to Telegram - will retry next time")
            else:
                print(f"⚠️ Telegram not configured - Post {i} not sent")
        else:
            if post_id:
                print(f"ℹ️ Post {i} already published to Telegram (skipping)")
            else:
                print(f"⚠️ Post {i} has no ID (skipping)")

    # Save updated published posts list
    if new_posts_published:
        save_published_posts(published_posts)
        print(f"\n📝 Updated published posts list with {len([p for p in recent_posts if p.get('id') and p.get('id') not in published_posts])} new posts")

    print(f"\n📊 Summary: {len(recent_posts)} posts fetched, {len(published_posts)} total posts tracked")